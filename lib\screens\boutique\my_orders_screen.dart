import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/order_service.dart';
import 'package:callitris/services/wallet_service.dart';
import 'package:callitris/widgets/payment_method_selector.dart';
import 'package:callitris/widgets/navigation_menu_button.dart';
import 'package:callitris/screens/payment/transaction_history_screen.dart';
import 'package:intl/intl.dart';
import 'dart:async';

import '../../utils/navigation_service.dart';
import '../../utils/image_utils.dart';
import 'order_detail_screen.dart';

class MyOrdersScreen extends StatefulWidget {
  const MyOrdersScreen({super.key});

  @override
  State<MyOrdersScreen> createState() => _MyOrdersScreenState();
}

class _MyOrdersScreenState extends State<MyOrdersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<Map<String, dynamic>> _activeOrders = [];
  List<Map<String, dynamic>> _completedOrders = [];
  String? _errorMessage;

  // Subscriptions pour les streams réactifs
  StreamSubscription<List<Map<String, dynamic>>>? _ordersSubscription;
  StreamSubscription<bool>? _loadingSubscription;
  StreamSubscription<String?>? _errorSubscription;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _setupStreams();
    _loadOrders();
  }

  /// Configure les streams réactifs pour les mises à jour automatiques
  void _setupStreams() {
    // Écouter les changements de commandes
    _ordersSubscription = OrderService.ordersStream.listen((orders) {
      if (mounted) {
        _updateOrdersFromStream(orders);
      }
    });

    // Écouter les changements d'état de chargement
    _loadingSubscription = OrderService.isLoadingOrdersStream.listen((
      isLoading,
    ) {
      if (mounted) {
        setState(() {
          _isLoading = isLoading;
        });
      }
    });

    // Écouter les erreurs
    _errorSubscription = OrderService.ordersErrorStream.listen((error) {
      if (mounted) {
        setState(() {
          _errorMessage = error;
        });
      }
    });
  }

  /// Met à jour les commandes à partir du stream
  void _updateOrdersFromStream(List<Map<String, dynamic>> allOrders) {
    final activeOrders = <Map<String, dynamic>>[];
    final completedOrders = <Map<String, dynamic>>[];

    for (final order in allOrders) {
      final status = order['status']?.toString().toLowerCase() ?? '';
      final reste = int.tryParse(order['reste']?.toString() ?? '0') ?? 0;

      if (status == 'completed' || reste <= 0) {
        completedOrders.add(order);
      } else {
        activeOrders.add(order);
      }
    }

    setState(() {
      _activeOrders = activeOrders;
      _completedOrders = completedOrders;
      _errorMessage = null;
    });
  }

  Future<void> _loadOrders() async {
    // Utiliser le service réactif pour rafraîchir les commandes
    await OrderService.refreshOrders();
  }

  @override
  void dispose() {
    // Nettoyer les subscriptions
    _ordersSubscription?.cancel();
    _loadingSubscription?.cancel();
    _errorSubscription?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  String _formatPrice(dynamic price) {
    if (price == null) return '0';

    final formatter = NumberFormat('#,###', 'fr_FR');
    int priceInt;

    if (price is int) {
      priceInt = price;
    } else if (price is double) {
      priceInt = price.toInt();
    } else if (price is String) {
      priceInt = int.tryParse(price) ?? 0;
    } else {
      priceInt = 0;
    }

    return formatter.format(priceInt).replaceAll(',', ' ');
  }

  // Fonction pour charger la monnaie disponible
  Future<double> _loadMonnaieDisponible() async {
    try {
      final result = await WalletService.getUserMonnaie();
      if (result['success']) {
        return (result['montant'] ?? 0).toDouble();
      }
      return 0.0;
    } catch (e) {
      print('Erreur lors du chargement de la monnaie: $e');
      return 0.0;
    }
  }

  void _showVersementDialog(
    BuildContext context,
    String orderId,
    int montantJournalier,
  ) {
    // Afficher d'abord la sélection du moyen de paiement
    _showPaymentMethodDialog(context, orderId, montantJournalier);
  }

  void _showPaymentMethodDialog(
    BuildContext context,
    String orderId,
    int montantJournalier,
  ) {
    _showAmountInputDialog(context, orderId, montantJournalier);
  }

  Widget _buildPaymentMethodButton({
    required BuildContext context,
    required String icon,
    required String name,
    required Color color,
    required VoidCallback onTap,
  }) {
    // URLs des logos des services de paiement
    final Map<String, String> logoUrls = {
      'Orange Money':
          'https://www.orange.ci/2/menu_resources/uploads/logo_11.png',
      'MTN Mobile Money':
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR8UGL6wp4LVORYnRGOuGYtDYzoOqLWOJIQiw&s',
      'Moov Money':
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTJzCy5RIBc5Hstu5ihcMA_jv-pSRMjI96sAw&s',
      'Wave':
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS_fU5FsOrX1NXgF4nB8lLEfIq9uvWi0J5v7g&s',
    };

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Utiliser l'image du logo du service de paiement
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.network(
                    logoUrls[name] ?? '',
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback en cas d'erreur de chargement de l'image
                      return Container(
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.account_balance_wallet,
                          color: color,
                          size: 20,
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  void _showUnavailablePaymentMethodDialog(
    BuildContext context,
    String methodName,
  ) {
    // URLs des logos des services de paiement
    final Map<String, String> logoUrls = {
      'Orange Money':
          'https://www.orange.ci/2/menu_resources/uploads/logo_11.png',
      'MTN Mobile Money':
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR8UGL6wp4LVORYnRGOuGYtDYzoOqLWOJIQiw&s',
      'Moov Money':
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTJzCy5RIBc5Hstu5ihcMA_jv-pSRMjI96sAw&s',
      'Wave':
          'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS_fU5FsOrX1NXgF4nB8lLEfIq9uvWi0J5v7g&s',
    };

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Logo du service sélectionné
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.red[100]!, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(40),
                    child: Stack(
                      children: [
                        // Logo du service
                        Image.network(
                          logoUrls[methodName] ?? '',
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                color: Colors.red[50],
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.error_outline,
                                size: 32,
                                color: Colors.red[400],
                              ),
                            );
                          },
                        ),
                        // Overlay rouge semi-transparent
                        Container(color: Colors.red.withOpacity(0.2)),
                        // Icône d'indisponibilité
                        Center(
                          child: Icon(
                            Icons.do_not_disturb_alt,
                            size: 32,
                            color: Colors.red[400],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Service indisponible',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF0A0A0A),
                    letterSpacing: -0.3,
                  ),
                ),
                // Reste du code inchangé...
                const SizedBox(height: 12),
                Text(
                  'Le service $methodName est actuellement indisponible. Veuillez utiliser Wave pour effectuer votre paiement.',
                  style: const TextStyle(
                    color: Color(0xFF8F9BB3),
                    fontSize: 14,
                    letterSpacing: -0.2,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          side: BorderSide(color: AppTheme.color.brunGris),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Annuler',
                          style: TextStyle(
                            color: AppTheme.color.brunGris,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _showPaymentMethodDialog(context, '', 0);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.color.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Réessayer',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAmountInputDialog(
    BuildContext context,
    String orderId,
    int montantJournalier,
  ) {
    final TextEditingController montantController = TextEditingController();
    montantController.text = montantJournalier.toString();

    // Récupérer le nombre de jours restants pour cette commande
    int versementsRestants = 0;
    for (var order in _activeOrders) {
      if (order['id'].toString() == orderId) {
        versementsRestants =
            int.tryParse(order['reste']?.toString() ?? '0') ?? 0;
        break;
      }
    }

    // Variables pour le calcul en temps réel
    int versementsComplets = 1; // Par défaut, 1 jour
    double monnaie = 0;
    bool excedentDetecte = false;

    // Variables pour la méthode de paiement
    PaymentMethod selectedPaymentMethod = PaymentMethod.wave;
    double monnaieDisponible = 0;

    // Fonction pour calculer les jours et la monnaie
    void calculerVersement(String value) {
      final double montantVerse = double.tryParse(value) ?? 0;
      // Calculer le nombre de jours que le montant peut couvrir
      int joursCouverts = (montantVerse / montantJournalier).floor();

      // Vérifier si ce nombre dépasse les jours restants
      excedentDetecte = joursCouverts > versementsRestants;

      if (excedentDetecte && versementsRestants > 0) {
        // Limiter les versements complets aux jours restants
        versementsComplets = versementsRestants;
        // Calculer la monnaie (tout ce qui dépasse le montant nécessaire pour les jours restants)
        monnaie = montantVerse - (versementsRestants * montantJournalier);
      } else {
        // Calcul normal
        versementsComplets = joursCouverts;
        monnaie = montantVerse - (joursCouverts * montantJournalier);
      }
    }

    // Calculer les valeurs initiales
    calculerVersement(montantController.text);

    // Charger la monnaie disponible
    _loadMonnaieDisponible().then((montant) {
      monnaieDisponible = montant;
    });

    // Utiliser un BuildContext local pour éviter les problèmes de contexte
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (BuildContext stateContext, StateSetter setState) {
            return AlertDialog(
              title: Text(
                'Effectuer un versement',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.color.textColor,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    Text(
                      'Montant journalier: ${_formatPrice(montantJournalier)} FCFA',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Jours restants: $versementsRestants',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),

                    // Champ de saisie du montant
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Montant à verser',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[800],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: TextField(
                            controller: montantController,
                            keyboardType: TextInputType.number,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            decoration: InputDecoration(
                              hintText: 'Ex: 25000',
                              hintStyle: TextStyle(
                                color: const Color(0xFF8F9BB3).withOpacity(0.6),
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 18,
                              ),
                              border: InputBorder.none,
                              suffixText: 'FCFA',
                              suffixStyle: TextStyle(
                                color: AppTheme.color.primaryColor,
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                              ),
                              prefixIcon: Padding(
                                padding: const EdgeInsets.only(
                                  left: 16,
                                  right: 8,
                                ),
                                child: Icon(
                                  Icons.payments_rounded,
                                  color: AppTheme.color.primaryColor,
                                  size: 24,
                                ),
                              ),
                              prefixIconConstraints: const BoxConstraints(
                                minWidth: 0,
                                minHeight: 0,
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                calculerVersement(value);
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Sélecteur de méthode de paiement
                    PaymentMethodSelector(
                      selectedMethod: selectedPaymentMethod,
                      onMethodChanged: (method) {
                        setState(() {
                          selectedPaymentMethod = method;
                        });
                      },
                      montantVerse:
                          double.tryParse(montantController.text) ?? 0,
                      monnaieDisponible: monnaieDisponible,
                    ),

                    const SizedBox(height: 24),

                    // Résumé du versement
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Résumé du versement',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[800],
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Jours couverts:',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                '$versementsComplets jour(s)',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Monnaie:',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                              Text(
                                '${_formatPrice(monnaie.toInt())} FCFA',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color:
                                      monnaie > 0
                                          ? AppTheme.color.greenColor
                                          : Colors.grey[800],
                                ),
                              ),
                            ],
                          ),

                          // Afficher un message d'information si un excédent est détecté
                          if (excedentDetecte && versementsRestants > 0)
                            Padding(
                              padding: const EdgeInsets.only(top: 16),
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.amber[50],
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.amber[200]!),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      color: Colors.amber[800],
                                      size: 20,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        'Le montant saisi couvre plus que les $versementsRestants jour(s) restant(s). L\'excédent de ${_formatPrice(monnaie.toInt())} FCFA sera ajouté à votre monnaie.',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.amber[900],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: Text(
                    'Annuler',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    // Récupérer le montant saisi
                    final double montantVerse =
                        double.tryParse(montantController.text) ?? 0;

                    if (montantVerse <= 0) {
                      ScaffoldMessenger.of(dialogContext).showSnackBar(
                        const SnackBar(
                          content: Text('Veuillez entrer un montant valide'),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Fermer le dialogue
                    Navigator.of(dialogContext).pop();

                    // Afficher un indicateur de chargement
                    BuildContext? loadingContext;
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (BuildContext ctx) {
                        loadingContext = ctx;
                        return const Center(child: CircularProgressIndicator());
                      },
                    );

                    try {
                      // Récupérer la clé de commande
                      String? commandeKey;
                      for (var order in _activeOrders) {
                        if (order['id'].toString() == orderId) {
                          commandeKey = order['cle']?.toString();
                          break;
                        }
                      }

                      // Appeler l'API selon la méthode de paiement sélectionnée
                      Map<String, dynamic> result;

                      if (selectedPaymentMethod.usesCinetPay) {
                        // Paiement via CinetPay
                        result = await OrderService.addVersementWithCinetPay(
                          context: context,
                          commandeId: orderId,
                          montant: montantVerse,
                          commandeKey: commandeKey,
                          monnaie: monnaie,
                        );
                      } else if (selectedPaymentMethod == PaymentMethod.wave) {
                        result = await OrderService.addVersementWithWave(
                          context: context,
                          commandeId: orderId,
                          montant: montantVerse,
                          commandeKey: commandeKey,
                          monnaie: monnaie,
                          itemId: orderId,
                        );
                      } else {
                        // Paiement avec la monnaie du portefeuille
                        result = await OrderService.addVersement(
                          orderId,
                          montantVerse,
                          monnaie: monnaie,
                          commandeKey: commandeKey,
                        );
                      }

                      // Fermer l'indicateur de chargement en vérifiant si le contexte est toujours valide
                      if (loadingContext != null &&
                          Navigator.canPop(loadingContext!)) {
                        Navigator.pop(loadingContext!);
                      }

                      if (result['success']) {
                        // Afficher un message de confirmation
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              excedentDetecte && versementsRestants > 0
                                  ? 'Versement de $versementsComplets jour(s) effectué avec ${_formatPrice(monnaie.toInt())} FCFA de monnaie'
                                  : (versementsComplets > 0
                                      ? 'Versement de $versementsComplets jour(s) effectué avec succès'
                                      : 'Monnaie de ${_formatPrice(monnaie.toInt())} FCFA ajoutée'),
                            ),
                            backgroundColor: Colors.green,
                          ),
                        );

                        // Recharger les commandes
                        _loadOrders();
                      } else {
                        // Afficher un message d'erreur
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              result['message'] ?? 'Erreur lors du versement',
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    } catch (e) {
                      // Fermer l'indicateur de chargement en vérifiant si le contexte est toujours valide
                      if (loadingContext != null &&
                          Navigator.canPop(loadingContext!)) {
                        Navigator.pop(loadingContext!);
                      }

                      // Afficher un message d'erreur
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Erreur: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Verser',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showPaymentSuccessDialog(
    BuildContext context,
    int versementsComplets,
    int monnaie,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppTheme.color.greenColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check_circle,
                    size: 48,
                    color: AppTheme.color.greenColor,
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Paiement réussi !',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF0A0A0A),
                    letterSpacing: -0.3,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  versementsComplets > 0
                      ? 'Versement de $versementsComplets jour(s) effectué avec succès${monnaie > 0 ? ' + ${_formatPrice(monnaie)} FCFA de monnaie' : ''}'
                      : 'Monnaie de ${_formatPrice(monnaie)} FCFA ajoutée',
                  style: const TextStyle(
                    color: Color(0xFF8F9BB3),
                    fontSize: 14,
                    letterSpacing: -0.2,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.greenColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Terminé',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order, bool isActive) {
    // Extraire les valeurs avec des valeurs par défaut pour éviter les nulls
    final String id = order['id']?.toString() ?? '';
    final String cle = order['cle']?.toString() ?? '';
    final String livret = order['livret']?.toString() ?? 'Livret inconnu';
    final String pack = order['pack']?.toString() ?? 'Pack inconnu';
    final String codeCmd = order['code_cmd']?.toString() ?? 'Code inconnu';
    final String journalier = order['journalier']?.toString() ?? '0';
    final int jour = int.tryParse(order['jour']?.toString() ?? '0') ?? 0;
    final int paye = int.tryParse(order['paye']?.toString() ?? '0') ?? 0;
    final int reste = int.tryParse(order['reste']?.toString() ?? '0') ?? 0;

    // Calculer la progression
    final double progress = jour > 0 ? (paye / jour).clamp(0.0, 1.0) : 0.0;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, Colors.orange.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.shade100, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () async {
            // Afficher un indicateur de chargement
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext context) {
                return const Center(child: CircularProgressIndicator());
              },
            );

            try {
              // Précharger les détails de la commande pour vérifier qu'ils sont disponibles
              final result = await OrderService.getOrderDetails(id);

              // Fermer l'indicateur de chargement
              Navigator.pop(context);

              if (result['success']) {
                // Naviguer vers l'écran de détails avec l'ID correct
                routeAnimation(context, OrderDetailScreen(orderId: id));
              } else {
                // Afficher un message d'erreur si les détails ne sont pas disponibles
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      result['message'] ??
                          'Impossible de charger les détails de la commande',
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            } catch (e) {
              // Fermer l'indicateur de chargement en cas d'erreur
              Navigator.pop(context);

              // Afficher un message d'erreur
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Erreur: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: AspectRatio(
                    aspectRatio: 15 / 8,
                    child:
                        order['photo_kit'] != null &&
                                order['photo_kit'].toString().isNotEmpty
                            ? Image.network(
                              ImageUtils.formatImageUrl(
                                order['photo_kit'].toString(),
                              ),
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                print(
                                  'Erreur de chargement d\'image: ${order['photo_kit']} - $error',
                                );
                                return _buildPlaceholderImage();
                              },
                            )
                            : _buildPlaceholderImage(),
                  ),
                ),
                const SizedBox(height: 16),

                Row(
                  children: [
                    Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.orange.shade200,
                            Colors.amber.shade300,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: const Icon(
                        Icons.shopping_bag_outlined,
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            livret,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            'Nº $codeCmd • $pack',
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          paye,
                          jour,
                          reste,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: _getStatusColor(
                            paye,
                            jour,
                            reste,
                          ).withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        _getStatusText(paye, jour, reste),
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(paye, jour, reste),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Section des statistiques
                Container(
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.orange.shade50, Colors.amber.shade50],
                    ),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: Colors.orange.shade100),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildInfoColumn('Journalier', '$journalier FCFA'),
                          _buildInfoColumn('Jours', jour.toString()),
                          _buildInfoColumn('Payés', paye.toString()),
                          _buildInfoColumn('Restants', reste.toString()),
                        ],
                      ),
                      const SizedBox(height: 14),

                      // Barre de progression
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Progression',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.orange.shade600,
                                ),
                              ),
                              Text(
                                '${(progress * 100).toInt()}%',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.orange.shade700,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Container(
                            height: 6,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(3),
                              color: Colors.grey.shade200,
                            ),
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: progress > 0 ? progress : 0.01,
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(3),
                                  color: _getProgressColor(progress),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 14),

                // Bouton d'action
                SizedBox(
                  width: double.infinity,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color.fromARGB(255, 255, 255, 255),
                          const Color.fromARGB(255, 217, 221, 249),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: AppTheme.color.primaryColor,
                        width: 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(10),
                      child: InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => OrderDetailScreen(
                                    orderId: order['id'].toString(),
                                  ),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(10),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.wallet,
                                size: 18,
                                color: AppTheme.color.primaryColor,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Effectuer un versement'.toUpperCase(),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.color.textColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Méthodes utilitaires pour le nouveau design
  Widget _buildInfoColumn(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(int paye, int jour, int reste) {
    if (reste == 0) {
      return Colors.green.shade600;
    } else if (paye > jour / 2) {
      return Colors.orange.shade600;
    } else {
      return Colors.blue.shade600;
    }
  }

  String _getStatusText(int paye, int jour, int reste) {
    if (reste == 0) {
      return 'Terminé';
    } else if (paye > jour / 2) {
      return 'Bientôt fini';
    } else {
      return 'En cours';
    }
  }

  Color _getProgressColor(double progress) {
    double percentage = progress * 100;
    if (percentage >= 100) {
      return Colors.green.shade400;
    } else if (percentage >= 34) {
      return Colors.orange.shade400;
    } else {
      return Colors.red.shade400;
    }
  }

  Widget _buildActiveOrdersTab() {
    return _activeOrders.isEmpty
        ? _buildEmptyState(
          'Aucune commande en cours',
          'Parcourez notre boutique pour trouver des produits qui vous intéressent.',
          Icons.shopping_bag_outlined,
        )
        : ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          itemCount: _activeOrders.length,
          itemBuilder: (context, index) {
            return _buildOrderCard(_activeOrders[index], true);
          },
        );
  }

  Widget _buildCompletedOrdersTab() {
    return _completedOrders.isEmpty
        ? _buildEmptyState(
          'Aucune commande terminée',
          'Vos commandes terminées apparaîtront ici.',
          Icons.check_circle_outline,
        )
        : ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
          itemCount: _completedOrders.length,
          itemBuilder: (context, index) {
            return _buildOrderCard(_completedOrders[index], false);
          },
        );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppTheme.color.primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, size: 36, color: AppTheme.color.primaryColor),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: Color(0xFF0A0A0A),
                letterSpacing: -0.3,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF8F9BB3),
                height: 1.4,
                letterSpacing: -0.2,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: 220,
              height: 52,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shadowColor: AppTheme.color.primaryColor.withOpacity(0.3),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Parcourir la boutique',
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 15,
                    letterSpacing: -0.2,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF9FAFC),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Mes commandes',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: Color(0xFF0A0A0A),
            letterSpacing: -0.3,
          ),
        ),
        actions: [
          NavigationMenuButton(),
          IconButton(
            icon: const Icon(Icons.history, color: Color(0xFF0A0A0A)),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TransactionHistoryScreen(),
                ),
              );
            },
            tooltip: 'Historique des paiements',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadOrders,
        child:
            _isLoading
                ? Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.color.primaryColor,
                    strokeWidth: 3,
                  ),
                )
                : _errorMessage != null && _errorMessage!.isNotEmpty
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red[300],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Erreur',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          _errorMessage!,
                          textAlign: TextAlign.center,
                          style: TextStyle(color: AppTheme.color.brunGris),
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadOrders,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.color.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Réessayer'),
                      ),
                    ],
                  ),
                )
                : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildActiveOrdersTab(),
                    _buildCompletedOrdersTab(),
                  ],
                ),
      ),
    );
  }
}

Widget _buildPlaceholderImage() {
  return Container(
    color: const Color(0xFFF5F6FA),
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.shopping_bag, color: Colors.grey[400], size: 40),
          const SizedBox(height: 8),
          Text(
            'Callitris',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    ),
  );
}
