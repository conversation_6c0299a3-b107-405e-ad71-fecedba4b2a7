name: callitris
description: "CallitrisPay - Application de paiement mobile et de gestion de commandes"
publish_to: 'none'
version: 2.1.0+2

environment:
  sdk: ^3.7.0


dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  http: ^1.1.0
  shared_preferences: ^2.2.0
  pin_code_fields: ^8.0.1
  intl: ^0.18.1
  lottie: ^2.6.0
  package_info_plus: ^8.3.0
  url_launcher: ^6.3.1
  cached_network_image: ^3.3.0
  qr_flutter: ^4.1.0
  cinetpay: ^1.0.8
  webview_flutter: ^4.13.0
  app_links: ^6.3.2
  provider: ^6.1.2
  rxdart: ^0.28.0
  get_it: ^8.0.2
  connectivity_plus: ^6.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/Callitris.png"
  min_sdk_android: 21
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/Callitris.png"
flutter:


  uses-material-design: true

  assets:
    - assets/
    - assets/images/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
