import 'package:flutter/material.dart';
import 'package:callitris/utils/appTheme.dart';
import 'package:callitris/services/carnet_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/auth_service.dart';
import '../../services/order_service.dart';
import '../../services/cinetpay_service.dart';
import '../../utils/image_utils.dart';
import 'package:callitris/screens/boutique/my_orders_screen.dart';
import 'package:callitris/config/api_config.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import '../../widgets/payment_method_selector.dart';
import '../../widgets/navigation_menu_button.dart';
import '../../services/wallet_service.dart';

class CarnetElementsScreen extends StatefulWidget {
  final String carnetId;
  final String carnetName;
  final Map<String, dynamic> carnetData;
  final List<Map<String, dynamic>>? preloadedElements;

  const CarnetElementsScreen({
    super.key,
    required this.carnetId,
    required this.carnetName,
    required this.carnetData,
    this.preloadedElements,
  });

  @override
  State<CarnetElementsScreen> createState() => _CarnetElementsScreenState();
}

class _CarnetElementsScreenState extends State<CarnetElementsScreen>
    with WidgetsBindingObserver {
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  List<Map<String, dynamic>> _carnetElements = [];
  bool _viewAsGrid = true; // Pour basculer entre la vue grille et liste
  String journalier = '';

  // Variables pour la gestion des paiements
  String? storageCommandeId;
  String? storageClientId;
  String? transactionCode;

  // Variables pour stocker les informations de transaction en cours
  double? _pendingMontant;
  double? _pendingMonnaie;
  String? _pendingTransactionCode;
  String? _pendingItemId; // Pour stocker l'ID de l'item en cours de commande

  // Variables pour contrôler les modals de chargement
  bool _isProcessingOperation = false;

  @override
  void initState() {
    super.initState();

    // Ajouter l'observateur du cycle de vie de l'application
    WidgetsBinding.instance.addObserver(this);

    // Tester le formatage d'URL
    ImageUtils.testFormatImageUrl();

    // Initialiser l'application avec vérification des transactions en attente
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Initialiser les préférences
    await _initPrefs();

    // Utiliser les éléments préchargés si disponibles, sinon les charger
    if (widget.preloadedElements != null &&
        widget.preloadedElements!.isNotEmpty) {
      setState(() {
        _carnetElements = widget.preloadedElements!;
      });
    } else {
      _loadCarnetElements();
    }
  }

  @override
  void dispose() {
    // Marquer que le widget est en cours de destruction
    _isProcessingOperation = false;

    // Supprimer l'observateur du cycle de vie de l'application
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      print(
        '📱 App revenue au premier plan - vérification immédiate des transactions',
      );
      _initializeApp();
      // Vérifier immédiatement les transactions en attente quand l'app revient au premier plan
      _checkPendingTransactionsOnResume();
    }
  }

  Future<void> _initPrefs() async {
    transactionCode = await OrderService.readData('payment_code');
    storageCommandeId = await OrderService.readData('commande_id');
    storageClientId = await OrderService.readData('client_id');

    try {
      if (transactionCode != null && transactionCode!.isNotEmpty) {
        await checkTransactionWaveState();
      }
    } catch (error) {
      print('Erreur lors de l\'initialisation des préférences: $error');
    }
  }

  /// Vérifie s'il y a des transactions en attente et les traite
  Future<void> _checkPendingTransactions() async {
    try {
      // Utiliser la méthode centralisée pour charger les données
      await _loadPendingTransactionFromStorage();

      if (_pendingMontant != null &&
          _pendingTransactionCode != null &&
          _pendingItemId != null) {
        print(
          'Transaction en attente trouvée: $_pendingTransactionCode, montant: $_pendingMontant, item: $_pendingItemId',
        );

        // Détecter le type de transaction basé sur le format du code
        await _checkCinetPayTransactionStatus(_pendingTransactionCode!);
        await _checkWaveTransactionStatus(_pendingTransactionCode!);
      } else {
        print('Aucune transaction en attente complète trouvée');
      }
    } catch (e) {
      print('Erreur lors de la vérification des transactions en attente: $e');
    }
  }

  /// Vérifie les transactions en attente quand l'app revient au premier plan
  Future<void> _checkPendingTransactionsOnResume() async {
    try {
      // Utiliser la méthode centralisée pour charger les données
      await _loadPendingTransactionFromStorage();
      print(
        '🔄 Transaction en attente détectée au retour: $_pendingTransactionCode, montant: $_pendingMontant, item: $_pendingItemId',
      );

      // Détecter le type de transaction et démarrer la vérification immédiate + périodique
      if (_isCinetPayTransactionId(_pendingTransactionCode!)) {
        print(
          '🏦 Transaction CinetPay - vérification immédiate et démarrage du monitoring...',
        );
        // Vérification immédiate
        await _checkCinetPayTransactionStatus(_pendingTransactionCode!);
        // Démarrer la vérification périodique si la transaction est toujours en attente
        if (_pendingTransactionCode != null) {
          _startCinetPayPeriodicStatusCheck();
        }
      } else {
        print(
          '🌊 Transaction Wave - vérification immédiate et démarrage du monitoring...',
        );
        // Vérification immédiate
        await _checkWaveTransactionStatus(_pendingTransactionCode!);
        // Démarrer la vérification périodique si la transaction est toujours en attente
        if (_pendingTransactionCode != null) {
          _startPeriodicStatusCheck();
        }
      }
    } catch (e) {
      print('❌ Erreur lors de la vérification des transactions au retour: $e');
    }
  }

  /// Détermine si un ID de transaction appartient à CinetPay
  bool _isCinetPayTransactionId(String transactionId) {
    return transactionId.contains('CP_') ||
        transactionId.contains('CINETPAY') ||
        !transactionId.contains('-'); // Wave utilise souvent des tirets
  }

  /// Stocke les informations d'une transaction en attente
  Future<void> _storePendingTransaction(
    double montant,
    double monnaie,
    String itemId,
  ) async {
    try {
      print(
        'Stockage de la transaction en attente: montant=$montant, monnaie=$monnaie, item=$itemId',
      );

      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('pending_montant', montant);
      await prefs.setDouble('pending_monnaie', monnaie);
      await prefs.setString('pending_item_id', itemId);

      // Stocker dans les variables de classe
      _pendingMontant = montant;
      _pendingMonnaie = monnaie;
      _pendingItemId = itemId;

      print(
        'Transaction en attente stockée avec succès: montant=$_pendingMontant, monnaie=$_pendingMonnaie, item=$_pendingItemId',
      );

      // Vérification immédiate pour s'assurer que les données sont bien stockées
      final verification = await prefs.getDouble('pending_montant');
      print('Vérification stockage: montant récupéré = $verification');
    } catch (e) {
      print('Erreur lors du stockage de la transaction en attente: $e');
    }
  }

  /// Met à jour le code de transaction en attente
  Future<void> _updatePendingTransactionCode(String transactionCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_transaction_code', transactionCode);

      // Stocker dans les variables de classe
      _pendingTransactionCode = transactionCode;

      print('Code de transaction en attente mis à jour: $transactionCode');
    } catch (e) {
      print('Erreur lors de la mise à jour du code de transaction: $e');
    }
  }

  /// Nettoie les données de transaction en attente
  Future<void> _clearPendingTransaction() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('pending_montant');
      await prefs.remove('pending_monnaie');
      await prefs.remove('pending_transaction_code');
      await prefs.remove('pending_item_id');

      // Nettoyer les variables de classe
      _pendingMontant = null;
      _pendingMonnaie = null;
      _pendingTransactionCode = null;
      _pendingItemId = null;

      print('Données de transaction en attente nettoyées');
    } catch (e) {
      print('Erreur lors du nettoyage des données en attente: $e');
    }
  }

  /// Charge les données de transaction en attente depuis SharedPreferences
  Future<void> _loadPendingTransactionFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final pendingMontant = prefs.getString('pending_montant');
      final pendingMonnaie = prefs.getString('pending_monnaie');
      final pendingTransactionCode = prefs.getString(
        'pending_transaction_code',
      );
      final pendingItemId = prefs.getString('pending_item_id');
      transactionCode = prefs.getString('payment_code');
      storageCommandeId = prefs.getString('commande_id');
      storageClientId = prefs.getString('client_id');

      if (pendingMontant != null && pendingItemId != null) {
        // Mettre à jour les variables de classe
        _pendingMontant = double.tryParse(pendingMontant!) ?? 0.0;
        _pendingMonnaie = double.tryParse(pendingMonnaie!) ?? 0.0;
        _pendingTransactionCode = transactionCode ?? pendingTransactionCode;
        _pendingItemId = pendingItemId;

        print(
          'Données de transaction récupérées: montant=$_pendingMontant, monnaie=$_pendingMonnaie, item=$_pendingItemId, transaction=$_pendingTransactionCode',
        );
      } else {
        print(
          'Aucune donnée de transaction en attente trouvée dans le stockage',
        );
      }
    } catch (e) {
      print('Erreur lors du chargement des données en attente: $e');
    }
  }

  /// Vérifie le statut d'une transaction Wave spécifique
  Future<void> _checkWaveTransactionStatus(String transactionCode) async {
    try {
      final token = await AuthService.getAuthToken();

      if (token == null) {
        print('Token manquant pour vérification Wave');
        return;
      }

      final response = await http.post(
        Uri.parse("${ApiConfig.baseUrl}/paiement/wave_pay_chek.php"),
        body: {'transaction_code': transactionCode},
        headers: {'Authorization': 'Bearer $token'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('Wave transaction check: $responseData');

        if (responseData['payment_status'] == 'succeeded') {
          _showPaymentSuccess('succeeded');
        } else if (responseData['payment_status'] == 'processing') {
          _showPaymentSuccess('processing');
          print('Transaction encore en cours de traitement');
        } else {
          _showPaymentSuccess('failed');
        }
      } else {
        print('Erreur Wave check : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la vérification Wave : $error');
    }
  }

  Future<void> checkTransactionWaveState() async {
    if (transactionCode != null) {
      await _checkWaveTransactionStatus(transactionCode!);
    }
  }

  /// Vérifie le statut d'une transaction CinetPay spécifique
  Future<void> _checkCinetPayTransactionStatus(String transactionId) async {
    try {
      print('Vérification du statut CinetPay pour transaction: $transactionId');

      // Utiliser le service CinetPay pour vérifier le statut
      final statusResult = await CinetPayService.checkTransactionStatus(
        transactionId,
      );

      if (statusResult['success']) {
        final status = statusResult['status']?.toString().toUpperCase();
        print('Statut CinetPay reçu: $status');

        if (status == 'ACCEPTED' || status == 'SUCCEEDED') {
          // Paiement réussi, traiter la commande
          print('Paiement CinetPay réussi, traitement de la commande...');

          // Récupérer l'itemId depuis les données en attente
          if (_pendingItemId != null) {
            await _processSuccessfulOrderAfterPayment(_pendingItemId!);
            // Nettoyer les données en attente après succès
            await _clearPendingTransaction();
          } else {
            print('Erreur: _pendingItemId est null');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Erreur: informations de commande manquantes'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else if (status == 'PENDING' || status == 'PROCESSING') {
          print('Transaction CinetPay encore en cours: $status');
          // Continuer la vérification périodique
        } else {
          // Transaction échouée ou annulée
          print('Transaction CinetPay échouée: $status');
          await _clearPendingTransaction();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Paiement CinetPay échoué. Statut: $status'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        print(
          'Erreur lors de la vérification CinetPay: ${statusResult['message']}',
        );
      }
    } catch (error) {
      print('Erreur lors de la vérification CinetPay: $error');
    }
  }

  /// Traite un paiement réussi en effectuant la commande
  Future<void> _processSuccessfulPayment() async {
    try {
      // Si les variables de classe sont nulles, essayer de les récupérer depuis SharedPreferences
      if (_pendingMontant == null || _pendingItemId == null) {
        print(
          'Variables de classe nulles, récupération depuis SharedPreferences...',
        );
        await _loadPendingTransactionFromStorage();
      }

      print(
        'Traitement du paiement réussi: montant=$_pendingMontant, item=$_pendingItemId',
      );

      // Nettoyer les données en attente
      await _clearPendingTransaction();
      await _clearPaymentCodeAndReload();

      // Afficher le succès avec informations de vérification
      _showOrderSuccess(
        'Commande enregistrée avec succès !\n\n✓ Commande confirmée\n✓ Versement enregistré',
      );
    } catch (e) {
      print('Erreur lors du traitement du paiement réussi: $e');
      _showOrderError('Erreur lors du traitement du paiement: $e');
    }
  }

  Future<void> _clearPaymentCodeAndReload() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('payment_code');
    await prefs.remove('commande_id');
    await prefs.remove('client_id');
    if (!mounted) return;
    setState(() {
      transactionCode = null;
    });
  }

  /// Affiche un modal de chargement avec un message personnalisé
  void _showProcessingDialog(String message) {
    if (_isProcessingOperation || !mounted)
      return; // Éviter les doublons et vérifier mounted

    setState(() {
      _isProcessingOperation = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.6),
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop:
              () async => false, // Empêcher la fermeture avec le bouton retour
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 0,
            backgroundColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppTheme.color.primaryColor.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.color.primaryColor,
                      ),
                      strokeWidth: 3,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.color.textColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Veuillez patienter...',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.color.brunGris,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Ferme le modal de chargement
  void _hideProcessingDialog() {
    if (_isProcessingOperation && mounted) {
      setState(() {
        _isProcessingOperation = false;
      });
      try {
        Navigator.of(context).pop();
      } catch (e) {
        print('Erreur lors de la fermeture du modal: $e');
      }
    }
  }

  /// Affiche un message d'erreur de manière sécurisée
  void _showSafeSnackBar(String message, {Color? backgroundColor}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor ?? Colors.red,
        ),
      );
    }
  }

  Future<void> _loadCarnetElements() async {
    // Afficher le modal de chargement seulement si ce n'est pas le chargement initial
    bool showModal = !_isLoading;

    if (showModal) {
      _showProcessingDialog('Chargement des éléments...');
    } else {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });
    }

    try {
      final result = await CarnetService.getCarnetElements(widget.carnetId);

      if (result['success']) {
        final data = result['data'];
        print("Type de données reçues: ${data.runtimeType}");

        if (data is List && data.isNotEmpty) {
          if (data[0] is List) {
            print("Structure détectée: liste de listes");
            setState(() {
              _carnetElements = List<Map<String, dynamic>>.from(data[0]);
              _isLoading = false;
            });
          } else {
            print("Structure détectée: liste simple");
            setState(() {
              _carnetElements = List<Map<String, dynamic>>.from(data);
              _isLoading = false;
            });
          }
        } else if (data is Map && data.containsKey('id_kit')) {
          print("Structure détectée: élément unique");
          setState(() {
            _carnetElements = [Map<String, dynamic>.from(data)];
            _isLoading = false;
          });
        } else {
          print("Structure non reconnue ou vide");
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Format de données non reconnu';
          });
        }
      } else {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = result['message'];
        });
      }
    } catch (e) {
      print("Exception lors du chargement des éléments: $e");
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Erreur lors du chargement des éléments: $e';
      });
    } finally {
      // Fermer le modal de chargement si il était affiché
      if (showModal) {
        _hideProcessingDialog();
      }
    }
  }

  void _addToCart(Map<String, dynamic> item) async {
    // Vérifier si le widget est encore monté
    if (!mounted) return;

    _showProcessingDialog('Vérification des informations...');

    try {
      // Vérifier si l'utilisateur est connecté
      final isLoggedIn = await AuthService.isLoggedIn();

      // Vérifier à nouveau après l'opération async
      if (!mounted) {
        _hideProcessingDialog();
        return;
      }

      _hideProcessingDialog();

      if (!isLoggedIn) {
        _hideProcessingDialog();
        _showSafeSnackBar('Veuillez vous connecter pour commander');
        // Navigator.push(context, MaterialPageRoute(builder: (context) => const LoginScreen()));
        return;
      }
    } catch (e) {
      _hideProcessingDialog();
      _showSafeSnackBar('Erreur lors de la vérification: $e');
      return;
    }

    final String name = item['option_kit'] ?? item['nom'] ?? 'Pack sans nom';
    final String description =
        'Obligation du souscripteur, délai de paiement et pénalité Votre cotisationn’est pas remboursable. Vous devez obligatoirement solder votre cotisation avant d’obtenir votre pack choisi en fin de campagne.  En cas de fluctuation de prix d’un article contenu dans un pack, le client doit supporter un changement du  cout de l’article si ce changement est connu sur le plan national. Un délai de collecte vous est exigé. Les clients qui n’ayant pas pu respecter le délai indiqué auront un délai  de rigueur d’une semaine. Passé ce délai, ils seront reconduits pour la campagne suivante avec une pénalité de 35% qui sera déduite de leur collecte. Les clients n’ayant pas pu soldé  leur pack dans un délais de un (01) an devront changer de carnet avec  une pénalité de -35%';
    final String livraison = item['livraison'] ?? 'indisponible';
    final String delaiLivraison = item['delai_livraison'] ?? 'pas de délai';
    final String garantie = item['garantie'] ?? 'aucune';

    String price = 'N/A';
    if (item.containsKey('montant_total_kit') &&
        item['montant_total_kit'] != null) {
      price = _formatPrice(item['montant_total_kit']);
    } else if (item.containsKey('prix_kit') && item['prix_kit'] != null) {
      price = _formatPrice(item['prix_kit']);
    } else if (item.containsKey('prix') && item['prix'] != null) {
      price = _formatPrice(item['prix']);
    }

    // Traiter l'URL de l'image
    String imageUrl = '';
    if (item['photo_kit'] != null && item['photo_kit'].toString().isNotEmpty) {
      final String photoPath = item['photo_kit'].toString();
      if (photoPath.startsWith('http')) {
        imageUrl = photoPath;
      } else {
        // Nettoyer le chemin pour éviter les doublons de "app/"
        String cleanPath = photoPath;
        if (cleanPath.startsWith('app/')) {
          cleanPath = cleanPath.substring(4); // Enlever le préfixe "app/"
        }
        if (cleanPath.startsWith('public/')) {
          cleanPath = cleanPath.substring(7); // Enlever le préfixe "public/"
        }
        if (cleanPath.startsWith('../../')) {
          cleanPath = cleanPath.substring(6); // Enlever le préfixe "../../"
        }

        // Construire l'URL complète
        imageUrl =
            'https://app.callitris-distribution.com/app/public/$cleanPath';
      }
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Confirmer la commande',
              style: TextStyle(color: AppTheme.color.primaryColor),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (imageUrl.isNotEmpty)
                    Container(
                      height: 150,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey[100],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.network(
                          imageUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Icon(
                                Icons.image_not_supported_outlined,
                                size: 40,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Nom du produit
                  Text(
                    name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Prix
                  Row(
                    children: [
                      Icon(
                        Icons.monetization_on,
                        size: 18,
                        color: AppTheme.color.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$price FCFA',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: AppTheme.color.primaryColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Description courte
                  Text(
                    'Description:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.color.brunGris,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 16),

                  // Informations de livraison
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Informations de livraison:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                            color: AppTheme.color.textColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.local_shipping_outlined,
                              size: 16,
                              color: AppTheme.color.brunGris,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              livraison,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.brunGris,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 16,
                              color: AppTheme.color.brunGris,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              delaiLivraison,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.brunGris,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Note importante sur le paiement obligatoire
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.payment, color: Colors.orange, size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Paiement obligatoire : Vous devez effectuer un paiement avec statut "réussi" avant que votre commande ne soit validée.',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.orange.shade800,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Note
                  Text(
                    'En procédant au paiement, vous acceptez les conditions générales de vente.',
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                      color: AppTheme.color.brunGris,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Annuler',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _showPaymentMethodDialog(item);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Procéder au paiement'),
              ),
            ],
          ),
    );
  }

  void _showPaymentMethodDialog(Map<String, dynamic> item) async {
    // Récupérer le prix total et journalier
    final Map<String, dynamic>? user = await AuthService.getUserData();
    double totalPrice = 0.0;
    double dailyPrice = 0.0;

    if (item.containsKey('montant_total_kit') &&
        item['montant_total_kit'] != null) {
      totalPrice = double.tryParse(item['montant_total_kit'].toString()) ?? 0.0;
    } else if (item.containsKey('prix_kit') && item['prix_kit'] != null) {
      totalPrice = double.tryParse(item['prix_kit'].toString()) ?? 0.0;
    } else if (item.containsKey('prix') && item['prix'] != null) {
      totalPrice = double.tryParse(item['prix'].toString()) ?? 0.0;
    }

    if (item.containsKey('cout_journalier_kit') &&
        item['cout_journalier_kit'] != null) {
      dailyPrice =
          double.tryParse(item['cout_journalier_kit'].toString()) ?? 0.0;
    }

    if (totalPrice <= 0 || dailyPrice <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Prix du produit invalide'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Charger la monnaie disponible
    double monnaieDisponible = 0.0;
    try {
      final walletData = await WalletService.getUserMonnaie();
      if (walletData['success']) {
        monnaieDisponible = (walletData['montant'] ?? 0).toDouble();
      }
    } catch (e) {
      print('Erreur lors du chargement de la monnaie: $e');
    }

    PaymentMethod selectedMethod = PaymentMethod.wave;
    double customAmount = dailyPrice; // Montant par défaut = prix journalier

    final TextEditingController amountController = TextEditingController();
    final TextEditingController phoneController = TextEditingController();
    amountController.text = customAmount.toInt().toString();
    phoneController.text = user?['telephone_client'] ?? '010101010101';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setState) {
              // Fonction pour valider le montant
              bool isValidAmount(double amount) {
                return amount >= dailyPrice &&
                    amount <= totalPrice &&
                    amount % 5 == 0;
              }

              return AlertDialog(
                title: Text(
                  'Paiement personnalisé',
                  style: TextStyle(
                    color: AppTheme.color.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Informations sur le produit
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppTheme.color.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppTheme.color.primaryColor.withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item['option_kit'] ??
                                  item['nom'] ??
                                  'Pack sans nom',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Prix journalier: ${_formatPrice(dailyPrice.toInt())} FCFA',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              'Prix total: ${_formatPrice(totalPrice.toInt())} FCFA',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppTheme.color.brunGris,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Champ de saisie du montant
                      Text(
                        'Montant à payer (FCFA)',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: AppTheme.color.textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: amountController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: 'Ex: ${_formatPrice(dailyPrice.toInt())}',
                          prefixIcon: Icon(
                            Icons.payments,
                            color: AppTheme.color.primaryColor,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: AppTheme.color.primaryColor,
                            ),
                          ),
                        ),
                        onChanged: (value) {
                          final amount = double.tryParse(value) ?? 0.0;
                          setState(() {
                            customAmount = amount;
                          });
                        },
                      ),

                      const SizedBox(height: 12),

                      // Validation du montant
                      if (amountController.text.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                isValidAmount(customAmount)
                                    ? Colors.green.withOpacity(0.1)
                                    : Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color:
                                  isValidAmount(customAmount)
                                      ? Colors.green.withOpacity(0.3)
                                      : Colors.red.withOpacity(0.3),
                            ),
                          ),
                          child: Text(
                            _getAmountValidationMessage(
                              customAmount,
                              dailyPrice,
                              totalPrice,
                            ),
                            style: TextStyle(
                              color:
                                  isValidAmount(customAmount)
                                      ? Colors.green
                                      : Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                      const SizedBox(height: 16),

                      PaymentMethodSelector(
                        selectedMethod: selectedMethod,
                        onMethodChanged: (method) {
                          setState(() {
                            selectedMethod = method;
                          });
                        },
                        montantVerse: customAmount,
                        monnaieDisponible: monnaieDisponible,
                        enabled: true,
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Annuler',
                      style: TextStyle(color: AppTheme.color.brunGris),
                    ),
                  ),
                  ElevatedButton(
                    onPressed:
                        isValidAmount(customAmount) &&
                                (!selectedMethod.usesCinetPay ||
                                    (phoneController.text.trim().isNotEmpty &&
                                        _isValidPhoneNumber(
                                          phoneController.text.trim(),
                                        )))
                            ? () {
                              Navigator.of(context).pop();
                              _processPaymentAndOrder(
                                item,
                                selectedMethod,
                                customAmount,
                                phoneNumber: phoneController.text.trim(),
                              );
                            }
                            : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Payer maintenant'),
                  ),
                ],
              );
            },
          ),
    );
  }

  String _getAmountValidationMessage(
    double amount,
    double dailyPrice,
    double totalPrice,
  ) {
    if (amount < dailyPrice) {
      return 'Montant trop faible (minimum: ${_formatPrice(dailyPrice.toInt())} FCFA)';
    } else if (amount > totalPrice) {
      return 'Montant trop élevé (maximum: ${_formatPrice(totalPrice.toInt())} FCFA)';
    } else if (amount % 5 != 0) {
      return 'Le montant doit être un multiple de 5 FCFA';
    } else {
      final jours = (amount / dailyPrice).floor();
      return 'Versement valide pour $jours jour(s) de service';
    }
  }

  bool _isValidPhoneNumber(String phone) {
    // Supprimer espaces et caractères spéciaux
    String cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Format international: +225XXXXXXXXX (9 chiffres après +225)
    if (cleanPhone.startsWith('+225')) {
      return cleanPhone.length == 14 && // "+225" + 9 chiffres
          RegExp(r'^\+225[0-9]{10}$').hasMatch(cleanPhone);
    }
    // Format local: XXXXXXXXXX (10 chiffres)
    else {
      return cleanPhone.length == 10 &&
          RegExp(r'^[0-9]{10}$').hasMatch(cleanPhone);
    }
  }

  /// Vérifie que la commande et le versement sont bien enregistrés
  Future<Map<String, dynamic>> _verifyOrderAndPayment(
    String commandeId,
    double montant,
  ) async {
    try {
      print(
        'Début de la vérification: commandeId=$commandeId, montant=$montant',
      );

      // Attendre un peu pour laisser le temps au backend de traiter
      await Future.delayed(const Duration(seconds: 2));

      // Vérifier que la commande existe
      final orderVerification = await _verifyOrderExists(commandeId);
      if (!orderVerification['success']) {
        return {
          'success': false,
          'message': 'Commande non trouvée: ${orderVerification['message']}',
        };
      }

      // Vérifier que le versement est enregistré
      final paymentVerification = await _verifyPaymentExists(
        commandeId,
        montant,
      );
      if (!paymentVerification['success']) {
        return {
          'success': false,
          'message': 'Versement non trouvé: ${paymentVerification['message']}',
        };
      }

      return {
        'success': true,
        'message': 'Commande et versement vérifiés avec succès',
        'order': orderVerification['data'],
        'payment': paymentVerification['data'],
      };
    } catch (e) {
      print('Erreur lors de la vérification: $e');
      return {'success': false, 'message': 'Erreur de vérification: $e'};
    }
  }

  /// Vérifie que la commande existe dans le système
  Future<Map<String, dynamic>> _verifyOrderExists(String commandeId) async {
    try {
      final token = await AuthService.getAuthToken();
      if (token == null) {
        return {'success': false, 'message': 'Token manquant'};
      }

      final response = await http
          .get(
            Uri.parse(
              "${ApiConfig.baseUrl}/commande/getCommande.php?id=$commandeId",
            ),
            headers: {'Authorization': 'Bearer $token'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data != null && data is Map && data.isNotEmpty) {
          return {'success': true, 'message': 'Commande trouvée', 'data': data};
        }
      }

      return {
        'success': false,
        'message': 'Commande non trouvée (status: ${response.statusCode})',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de vérification commande: $e',
      };
    }
  }

  /// Vérifie que le versement existe pour la commande
  Future<Map<String, dynamic>> _verifyPaymentExists(
    String commandeId,
    double montant,
  ) async {
    try {
      final versements = await OrderService.getOrderVersements(commandeId);

      if (versements['success'] && versements['versements'] != null) {
        final List<dynamic> versementsList = versements['versements'];

        // Chercher un versement correspondant au montant
        for (var versement in versementsList) {
          if (versement is Map) {
            final versementMontant =
                double.tryParse(versement['montant']?.toString() ?? '0') ?? 0.0;
            if ((versementMontant - montant).abs() < 0.01) {
              // Tolérance de 1 centime
              return {
                'success': true,
                'message': 'Versement trouvé',
                'data': versement,
              };
            }
          }
        }
      }

      return {
        'success': false,
        'message': 'Versement non trouvé pour le montant $montant',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de vérification versement: $e',
      };
    }
  }

  void _processPaymentAndOrder(
    Map<String, dynamic> item,
    PaymentMethod paymentMethod,
    double totalPrice, {
    String phoneNumber = '',
  }) async {
    final String itemId =
        item['id_kit']?.toString() ?? item['id']?.toString() ?? '';

    if (itemId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Impossible d\'identifier le produit. Veuillez réessayer.',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Afficher le modal de chargement approprié selon la méthode de paiement
    String loadingMessage = 'Initialisation du paiement...';
    if (paymentMethod.usesCinetPay) {
      loadingMessage = 'Initialisation du paiement CinetPay...';
    } else if (paymentMethod == PaymentMethod.wave) {
      loadingMessage = 'Initialisation du paiement Wave...';
    } else if (paymentMethod == PaymentMethod.monnaie) {
      loadingMessage = 'Traitement du paiement avec votre monnaie...';
    }

    _showProcessingDialog(loadingMessage);

    try {
      // Traitement selon la méthode de paiement sélectionnée
      if (paymentMethod.usesCinetPay) {
        await _processCinetPayPayment(item, totalPrice, phoneNumber);
      } else if (paymentMethod == PaymentMethod.wave) {
        await _processWavePayment(item, totalPrice);
      } else if (paymentMethod == PaymentMethod.monnaie) {
        await _processMonnaiePayment(item, totalPrice);
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      _showSafeSnackBar('Erreur: $e');
    }
  }

  Future<void> _processCinetPayPayment(
    Map<String, dynamic> item,
    double totalPrice,
    String phoneNumber,
  ) async {
    try {
      final String itemId =
          item['id_kit']?.toString() ?? item['id']?.toString() ?? '';
      final String itemName = item['option_kit'] ?? item['nom'] ?? 'Produit';
      final user = await AuthService.getUserData();

      // Stocker les informations de transaction en attente pour CinetPay aussi
      await _storePendingTransaction(totalPrice, 0.0, itemId);

      // Vérifier le contexte avant l'appel async
      if (!mounted) {
        _hideProcessingDialog();
        return;
      }

      // Initialiser le paiement CinetPay
      final initResult = await CinetPayService.initializePayment(
        context: context,
        amount: totalPrice,
        description: 'Commande $itemName',
        customerName: user?['nom_client'] ?? 'Client Callitris',
        customerEmail: user?['email'] ?? '<EMAIL>',
        customerPhone: user?['telephone_client'] ?? '0101010101',
        metadata: {
          'type': 'carnet_order',
          'itemId': itemId,
          'itemName': itemName,
          'isCarnet': true,
        },
      );

      if (!initResult['success']) {
        // Fermer le dialogue de chargement
        _hideProcessingDialog();

        // Nettoyer les données en attente en cas d'échec
        await _clearPendingTransaction();

        _showSafeSnackBar(initResult['message'] ?? 'Erreur d\'initialisation');
        return;
      }

      // Stocker l'ID de transaction CinetPay pour la vérification
      final transaction = initResult['transaction'];
      if (transaction != null) {
        await _updatePendingTransactionCode(transaction.id);
      }

      // Vérifier le contexte avant le deuxième appel async
      if (!mounted) {
        _hideProcessingDialog();
        return;
      }

      // Lancer le processus de paiement
      final paymentResult = await CinetPayService.processPayment(
        context: context,
        amount: totalPrice,
        transaction: initResult['transaction'],
        customerPhone: phoneNumber.isNotEmpty ? phoneNumber : '0101010101',
      );

      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      if (paymentResult['success']) {
        // Vérifier le statut de la transaction
        final status = paymentResult['status']?.toString().toUpperCase();

        if (status == 'ACCEPTED' || status == 'SUCCEEDED') {
          // Paiement réussi, enregistrer la commande
          await _processSuccessfulOrderAfterPayment(itemId);
          // Nettoyer les données en attente après succès
          await _clearPendingTransaction();
        } else if (status == 'PENDING' || status == 'PROCESSING') {
          // Transaction en cours, démarrer la vérification périodique
          _showSafeSnackBar(
            'Paiement en cours de traitement. Vérification automatique...',
            backgroundColor: Colors.orange,
          );
          _startCinetPayPeriodicStatusCheck();
        } else {
          // Nettoyer les données en attente en cas d'échec
          await _clearPendingTransaction();

          _showSafeSnackBar('Transaction non acceptée. Statut: $status');
        }
      } else {
        // Démarrer la vérification périodique même en cas d'échec apparent
        // car l'utilisateur pourrait avoir fermé la WebView prématurément
        if (transaction != null) {
          _showSafeSnackBar(
            'Vérification du statut de la transaction en cours...',
            backgroundColor: Colors.orange,
          );
          _startCinetPayPeriodicStatusCheck();
        } else {
          // Nettoyer les données en attente si pas de transaction
          await _clearPendingTransaction();

          _showSafeSnackBar(paymentResult['message'] ?? 'Erreur de paiement');
        }
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      // Nettoyer les données en attente en cas d'erreur
      await _clearPendingTransaction();

      _showSafeSnackBar('Erreur CinetPay: $e');
    }
  }

  Future<void> _processWavePayment(
    Map<String, dynamic> item,
    double totalPrice,
  ) async {
    try {
      final String itemId =
          item['id_kit']?.toString() ?? item['id']?.toString() ?? '';

      // Stocker les informations de transaction en attente
      await _storePendingTransaction(totalPrice, 0.0, itemId);

      // Vérifier le contexte avant l'appel async
      if (!mounted) {
        _hideProcessingDialog();
        return;
      }

      // Initialiser le paiement Wave
      final result = await OrderService.addVersementWithWave(
        context: context,
        commandeId: '', // Pas encore de commande créée
        montant: totalPrice,
        monnaie: 0.0,
        itemId: itemId,
      );

      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      if (result['success']) {
        if (result['paymentId'] != null) {
          await _updatePendingTransactionCode(result['paymentId']);
        }

        _showSafeSnackBar(
          result['message'] ?? 'Paiement Wave initié. Vérification en cours...',
          backgroundColor: Colors.orange,
        );

        // Démarrer la vérification périodique du statut
        _startPeriodicStatusCheck();
      } else {
        // Nettoyer les données en attente en cas d'échec
        await _clearPendingTransaction();

        _showSafeSnackBar(result['message'] ?? 'Erreur lors du paiement Wave');
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      // Nettoyer les données en attente
      await _clearPendingTransaction();

      _showSafeSnackBar('Erreur Wave: $e');
    }
  }

  Future<void> _processMonnaiePayment(
    Map<String, dynamic> item,
    double totalPrice,
  ) async {
    try {
      final String itemId =
          item['id_kit']?.toString() ?? item['id']?.toString() ?? '';

      // Vérifier le solde disponible
      final walletData = await WalletService.getUserMonnaie();
      double monnaieDisponible = 0.0;

      if (walletData['success']) {
        monnaieDisponible = (walletData['montant'] ?? 0).toDouble();
      }

      if (monnaieDisponible < totalPrice) {
        // Fermer le dialogue de chargement
        _hideProcessingDialog();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Solde insuffisant. Disponible: ${_formatPrice(monnaieDisponible.toInt())} FCFA, Requis: ${_formatPrice(totalPrice.toInt())} FCFA',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Effectuer le paiement avec la monnaie du portefeuille
      // Pour cela, on crée d'abord la commande puis on effectue le versement
      final orderResult = await OrderService.placeOrder(itemId, isCarnet: true);

      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      if (orderResult['success']) {
        // Commande créée avec succès, considérer le paiement comme réussi
        _showOrderSuccess(
          orderResult['message'] ?? 'Commande enregistrée avec succès !',
        );
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                orderResult['message'] ?? 'Erreur lors de la commande',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      _hideProcessingDialog();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur paiement monnaie: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _processSuccessfulOrderAfterPayment(String itemId) async {
    try {
      // Effectuer la commande via l'API
      final result = await OrderService.placeOrder(itemId, isCarnet: true);

      if (result['success']) {
        // Commande réussie
        print('Commande effectuée avec succès');
        print("message : ${result['message']}");

        // Enregistrer le versement après le succès de la commande
        try {
          final commandeId = result['commande_id']?.toString();
          if (commandeId != null && _pendingMontant != null) {
            print(
              'Enregistrement du versement: commandeId=$commandeId, montant=$_pendingMontant',
            );

            final versementResult = await OrderService.addVersement(
              commandeId,
              _pendingMontant!,
              monnaie: _pendingMonnaie ?? 0.0,
              transactionCode: transactionCode,
            );

            if (versementResult['success']) {
              print('Versement enregistré avec succès');

              // Nettoyer les données en attente
              await _clearPendingTransaction();
              await _clearPaymentCodeAndReload();

              // Afficher le succès avec informations de vérification
              _showOrderSuccess(
                '${result['message'] ?? 'Commande enregistrée avec succès !'}\n\n✓ Commande confirmée\n✓ Versement enregistré',
              );
            } else {
              print(
                'Erreur lors de l\'enregistrement du versement: ${versementResult['message']}',
              );
              // Afficher l'erreur de versement
              _showOrderError(
                versementResult['message'] ??
                    'Erreur lors de l\'enregistrement du versement',
              );
            }
          } else {
            print(
              "Commande ID invalide : $commandeId ou montant manquant : $_pendingMontant",
            );
          }
        } catch (e) {
          print('Erreur lors de l\'enregistrement du versement: $e');
        }
      } else {
        print(
          'Erreur lors de l\'enregistrement de la commande: ${result['message']}',
        );
        _showOrderError(
          result['message'] ??
              'Erreur lors de l\'enregistrement de la commande',
        );
      }
    } catch (e) {
      print('Erreur lors du traitement de la commande après paiement: $e');
      _showOrderError('Erreur lors du traitement de la commande: $e');
    }
  }

  void _startPeriodicStatusCheck() {
    // Vérifier le statut toutes les 5 secondes pendant 2 minutes maximum
    int attempts = 0;
    const maxAttempts = 24; // 2 minutes / 5 secondes

    Timer.periodic(const Duration(seconds: 5), (timer) async {
      attempts++;

      if (attempts >= maxAttempts || !mounted) {
        timer.cancel();
        return;
      }

      if (_pendingTransactionCode != null) {
        await _checkWaveTransactionStatus(_pendingTransactionCode!);

        // Si la transaction est traitée, arrêter la vérification
        if (_pendingTransactionCode == null) {
          timer.cancel();
        }
      } else {
        timer.cancel();
      }
    });
  }

  void _startCinetPayPeriodicStatusCheck() {
    // Vérifier le statut toutes les 10 secondes pendant 3 minutes maximum
    int attempts = 0;
    const maxAttempts = 18; // 3 minutes / 10 secondes

    Timer.periodic(const Duration(seconds: 10), (timer) async {
      attempts++;

      if (attempts >= maxAttempts || !mounted) {
        timer.cancel();
        // Nettoyer les données en attente après timeout
        if (mounted) {
          await _clearPendingTransaction();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Délai de vérification dépassé. Veuillez vérifier manuellement.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      if (_pendingTransactionCode != null) {
        await _checkCinetPayTransactionStatus(_pendingTransactionCode!);

        // Si la transaction est traitée, arrêter la vérification
        if (_pendingTransactionCode == null) {
          timer.cancel();
        }
      } else {
        timer.cancel();
      }
    });
  }

  void _processOrder(Map<String, dynamic> item) async {
    // Afficher un dialogue de chargement
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(child: CircularProgressIndicator());
      },
    );

    try {
      final String itemId =
          item['id_kit']?.toString() ?? item['id']?.toString() ?? '';

      if (itemId.isEmpty) {
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Impossible d\'identifier le produit. Veuillez réessayer.',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final result = await OrderService.placeOrder(itemId, isCarnet: true);

      Navigator.pop(context);

      if (result['success']) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                title: Text(
                  'Commande confirmée',
                  style: TextStyle(
                    color: AppTheme.color.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppTheme.color.greenColor,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      result['message'] ??
                          'Votre commande a été enregistrée avec succès !',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Vous pouvez suivre l\'état de votre commande dans la section "Mes commandes".',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                actions: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.color.primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      } else {
        // Vérifier si c'est une erreur de stock
        if (result.containsKey('stockError') && result['stockError'] == true) {
          // Afficher un dialogue spécifique pour les ruptures de stock
          showDialog(
            context: context,
            barrierDismissible: false,
            builder:
                (context) => AlertDialog(
                  title: const Text(
                    'Produit indisponible',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.inventory_2_outlined,
                        color: Colors.red,
                        size: 64,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        result['message'] ??
                            'Ce produit est actuellement en rupture de stock.',
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Veuillez essayer un autre produit ou réessayer plus tard.',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                  actions: [
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.color.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Compris'),
                    ),
                  ],
                ),
          );
        } else {
          // Afficher un message d'erreur général
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Erreur lors de la commande'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Fermer le dialogue de chargement
      Navigator.pop(context);

      // Afficher un message d'erreur
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          widget.carnetName,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: AppTheme.color.textColor,
            letterSpacing: -0.5,
          ),
        ),
        actions: [
          NavigationMenuButton(),
          // Bouton pour basculer entre la vue grille et liste
          IconButton(
            icon: Icon(
              _viewAsGrid ? Icons.view_list : Icons.grid_view,
              color: AppTheme.color.primaryColor,
            ),
            onPressed: () {
              setState(() {
                _viewAsGrid = !_viewAsGrid;
              });
            },
          ),
          // Bouton pour actualiser
          IconButton(
            icon: Icon(Icons.refresh, color: AppTheme.color.primaryColor),
            onPressed: _loadCarnetElements,
          ),
        ],
      ),
      body:
          _isLoading
              ? _buildLoadingState()
              : _hasError
              ? _buildErrorState()
              : _carnetElements.isEmpty
              ? _buildEmptyState()
              : _buildElementsList(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red[300]),
            const SizedBox(height: 24),
            Text(
              'Erreur de chargement',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage,
              style: TextStyle(fontSize: 16, color: AppTheme.color.brunGris),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: 200,
              height: 50,
              child: ElevatedButton(
                onPressed: _loadCarnetElements,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Réessayer',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 24),
            Text(
              'Aucun élément disponible',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.color.textColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Ce carnet ne contient pas encore d\'éléments',
              style: TextStyle(fontSize: 16, color: AppTheme.color.brunGris),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: 200,
              height: 50,
              child: ElevatedButton(
                onPressed: _loadCarnetElements,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Actualiser',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildElementsList() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // En-tête avec informations sur le carnet
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppTheme.color.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.inventory_2,
                    color: AppTheme.color.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          'Éléments du carnet',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.color.textColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Flexible(
                        child: Text(
                          '${_carnetElements.length} pack(s) disponible(s)',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.color.brunGris,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Liste ou grille des éléments
          _viewAsGrid ? _buildElementsGrid() : _buildElementsListView(),
        ],
      ),
    );
  }

  Widget _buildElementsGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _carnetElements.length,
      itemBuilder: (context, index) {
        final element = _carnetElements[index];
        return _buildElementCard(element);
      },
    );
  }

  Widget _buildElementsListView() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _carnetElements.length,
      itemBuilder: (context, index) {
        final element = _carnetElements[index];
        return _buildElementListItem(element);
      },
    );
  }

  Widget _buildElementCard(Map<String, dynamic> element) {
    // Log pour déboguer
    print("Élément à afficher: $element");

    // Extraire les valeurs avec des fallbacks
    final String id = element['id_kit']?.toString() ?? 'ID inconnu';
    final String name =
        element['option_kit'] ?? element['nom'] ?? 'Pack sans nom';
    final String description =
        element['description_kit'] ??
        element['description'] ??
        'Aucune description disponible';
    final String journalier =
        element['cout_journalier_kit']?.toString() ?? 'N/A';
    final String dateFinCotisation =
        element['date_fin_cotisation']?.toString() ?? 'Date inconnue';

    // Traiter le prix avec plus de robustesse
    String price = 'N/A';
    if (element.containsKey('montant_total_kit') &&
        element['montant_total_kit'] != null) {
      price = _formatPrice(element['montant_total_kit']);
    } else if (element.containsKey('prix_kit') && element['prix_kit'] != null) {
      price = _formatPrice(element['prix_kit']);
    } else if (element.containsKey('prix') && element['prix'] != null) {
      price = _formatPrice(element['prix']);
    }

    // Considérer tous les produits comme disponibles par défaut
    final bool isInStock = true;

    // Traiter l'URL de l'image (priorité à carnet_img, puis photo_kit)
    String imageUrl = '';
    String imagePath = '';

    // Vérifier d'abord carnet_img
    if (element['carnet_img'] != null &&
        element['carnet_img'].toString().isNotEmpty) {
      imagePath = element['carnet_img'].toString();
    }
    // Sinon utiliser photo_kit comme fallback
    else if (element['photo_kit'] != null &&
        element['photo_kit'].toString().isNotEmpty) {
      imagePath = element['photo_kit'].toString();
    }

    if (imagePath.isNotEmpty) {
      if (imagePath.startsWith('http')) {
        imageUrl = imagePath;
      } else {
        // Nettoyer le chemin pour éviter les doublons de "app/"
        String cleanPath = imagePath;
        if (cleanPath.startsWith('app/')) {
          cleanPath = cleanPath.substring(4); // Enlever le préfixe "app/"
        }
        if (cleanPath.startsWith('public/')) {
          cleanPath = cleanPath.substring(7); // Enlever le préfixe "public/"
        }
        if (cleanPath.startsWith('../../')) {
          cleanPath = cleanPath.substring(6); // Enlever le préfixe "../../"
        }

        // Éviter le doublon de "public/" dans l'URL
        if (cleanPath.startsWith('public/')) {
          cleanPath = cleanPath.substring(
            7,
          ); // Enlever le second préfixe "public/"
        }

        // Construire l'URL complète
        imageUrl =
            'https://app.callitris-distribution.com/app/public/$cleanPath';

        // Afficher l'URL pour débogage
        print("URL d'image formatée (carte): $imageUrl");
      }
    }

    // Générer une couleur basée sur l'ID
    final int colorSeed = id.hashCode;
    final List<Color> cardColors = [
      Color(0xFF5E72E4), // Bleu Djamo
      Color(0xFF11CDEF), // Bleu clair
      Color(0xFFFB6340), // Orange
      Color(0xFF2DCE89), // Vert
      Color(0xFFF5365C), // Rose
      Color(0xFFFFD600), // Jaune
    ];
    final Color cardColor = cardColors[colorSeed % cardColors.length];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Action lors du tap sur un élément
            _showElementDetails(element);
          },
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image ou placeholder - réduite
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                child: Container(
                  height: 100,
                  width: double.infinity,
                  decoration: BoxDecoration(color: cardColor.withOpacity(0.1)),
                  child:
                      imageUrl.isNotEmpty
                          ? Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              print("Erreur de chargement d'image: $error");
                              return Center(
                                child: Icon(
                                  Icons.image_not_supported_outlined,
                                  size: 40,
                                  color: cardColor,
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value:
                                      loadingProgress.expectedTotalBytes != null
                                          ? loadingProgress
                                                  .cumulativeBytesLoaded /
                                              loadingProgress
                                                  .expectedTotalBytes!
                                          : null,
                                  color: cardColor,
                                  strokeWidth: 2,
                                ),
                              );
                            },
                          )
                          : Center(
                            child: Icon(
                              Icons.inventory_2,
                              size: 40,
                              color: cardColor,
                            ),
                          ),
                ),
              ),

              // Contenu - avec Flexible pour s'adapter
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Badge de disponibilité - plus compact
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isInStock
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          'Disponible',
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                            color: isInStock ? Colors.green : Colors.red,
                          ),
                        ),
                      ),

                      const SizedBox(height: 3),

                      // Nom de l'élément - flexible
                      Text(
                        name,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.textColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 1),

                      // Description - flexible
                      Text(
                        'Journalier: $journalier FCFA',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppTheme.color.brunGris,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        'Fin de cotisation: $dateFinCotisation',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.redColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 1),

                      // Prix - en bas
                      Text(
                        '$price FCFA',
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: cardColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildElementListItem(Map<String, dynamic> element) {
    // Extraire les valeurs avec des fallbacks
    final String id = element['id_kit']?.toString() ?? 'ID inconnu';
    final String name =
        element['option_kit'] ?? element['nom'] ?? 'Pack sans nom';
    final String description =
        element['description_kit'] ??
        element['description'] ??
        'Aucune description disponible';
    final String journalier =
        element['cout_journalier_kit']?.toString() ?? 'N/A';

    // Traiter le prix avec plus de robustesse
    String price = 'N/A';
    if (element.containsKey('montant_total_kit') &&
        element['montant_total_kit'] != null) {
      price = _formatPrice(element['montant_total_kit']);
    } else if (element.containsKey('prix_kit') && element['prix_kit'] != null) {
      price = _formatPrice(element['prix_kit']);
    } else if (element.containsKey('prix') && element['prix'] != null) {
      price = _formatPrice(element['prix']);
    }

    // Considérer tous les produits comme disponibles par défaut
    final bool isInStock = true;

    // Générer une couleur basée sur l'ID
    final int colorSeed = id.hashCode;
    final List<Color> cardColors = [
      Color(0xFF5E72E4), // Bleu Djamo
      Color(0xFF11CDEF), // Bleu clair
      Color(0xFFFB6340), // Orange
      Color(0xFF2DCE89), // Vert
      Color(0xFFF5365C), // Rose
      Color(0xFFFFD600), // Jaune
    ];
    final Color cardColor = cardColors[colorSeed % cardColors.length];

    // Traiter l'URL de l'image (priorité à carnet_img, puis photo_kit)
    String imageUrl = '';
    String imagePath = '';

    // Vérifier d'abord carnet_img
    if (element['carnet_img'] != null &&
        element['carnet_img'].toString().isNotEmpty) {
      imagePath = element['carnet_img'].toString();
    }
    // Sinon utiliser photo_kit comme fallback
    else if (element['photo_kit'] != null &&
        element['photo_kit'].toString().isNotEmpty) {
      imagePath = element['photo_kit'].toString();
    }

    if (imagePath.isNotEmpty) {
      if (imagePath.startsWith('http')) {
        imageUrl = imagePath;
      } else {
        // Nettoyer le chemin pour éviter les doublons de "app/"
        String cleanPath = imagePath;
        if (cleanPath.startsWith('app/')) {
          cleanPath = cleanPath.substring(4); // Enlever le préfixe "app/"
        }
        if (cleanPath.startsWith('public/')) {
          cleanPath = cleanPath.substring(7); // Enlever le préfixe "public/"
        }
        if (cleanPath.startsWith('../../')) {
          cleanPath = cleanPath.substring(6); // Enlever le préfixe "../../"
        }

        // Éviter le doublon de "public/" dans l'URL
        if (cleanPath.startsWith('public/')) {
          cleanPath = cleanPath.substring(
            7,
          ); // Enlever le second préfixe "public/"
        }

        // Construire l'URL complète
        imageUrl =
            'https://app.callitris-distribution.com/app/public/$cleanPath';

        // Afficher l'URL pour débogage
        print("URL d'image formatée (liste): $imageUrl");
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Action lors du tap sur un élément
            _showElementDetails(element);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Image ou placeholder
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: cardColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child:
                      imageUrl.isNotEmpty
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              imageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                print("Erreur de chargement d'image: $error");
                                return Center(
                                  child: Icon(
                                    Icons.image_not_supported_outlined,
                                    size: 32,
                                    color: cardColor,
                                  ),
                                );
                              },
                            ),
                          )
                          : Center(
                            child: Icon(
                              Icons.inventory_2,
                              size: 32,
                              color: cardColor,
                            ),
                          ),
                ),

                const SizedBox(width: 16),

                // Contenu
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Badge de disponibilité
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isInStock
                                  ? Colors.green.withOpacity(0.1)
                                  : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Disponible',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Nom de l'élément
                      Text(
                        name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.color.textColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Description
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.color.brunGris,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Prix
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '$price FCFA',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: cardColor,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Bouton d'action
                    SizedBox(
                      width: 36,
                      height: 36,
                      child: Material(
                        color: isInStock ? cardColor : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(18),
                        child: InkWell(
                          onTap: isInStock ? () => _addToCart(element) : null,
                          borderRadius: BorderRadius.circular(18),
                          child: const Icon(
                            Icons.add_shopping_cart,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showElementDetails(Map<String, dynamic> element) {
    // Extraire les valeurs avec des fallbacks
    final String id = element['id_kit']?.toString() ?? 'ID inconnu';
    final String name =
        element['option_kit'] ?? element['nom'] ?? 'Pack sans nom';
    final String description =
        'Obligation du souscripteur, délai de paiement et pénalité.';
    final String description2 = 'Votre cotisation n’est pas remboursable.';
    final String description3 =
        'Vous devez obligatoirement solder votre cotisation avant d’obtenir votre pack choisi en fin de campagne.  En cas de fluctuation de prix d’un article contenu dans un pack, le client doit supporter un changement du  cout de l’article si ce changement est connu sur le plan national. Un délai de collecte vous est exigé. Les clients qui n’ayant pas pu respecter le délai indiqué auront un délai  de rigueur d’une semaine. Passé ce délai, ils seront reconduits pour la campagne suivante avec une pénalité de';
    final String description4 = '35%';
    final String description5 = 'qui sera déduite de leur collecte.';
    final String description6 =
        'Les clients n’ayant pas pu soldé  leur pack dans un délais de un (01) an devront changer de carnet avec  une pénalité de -35%.';

    String price = 'N/A';
    if (element.containsKey('montant_total_kit') &&
        element['montant_total_kit'] != null) {
      price = _formatPrice(element['montant_total_kit']);
    } else if (element.containsKey('prix_kit') && element['prix_kit'] != null) {
      price = _formatPrice(element['prix_kit']);
    } else if (element.containsKey('prix') && element['prix'] != null) {
      price = _formatPrice(element['prix']);
    }

    // Extraire la durée
    String duration = '150';
    String journalier = 'N/A';
    if (element.containsKey('cout_journalier_kit') &&
        element['cout_journalier_kit'] != null) {
      journalier = element['cout_journalier_kit'].toString();
    }

    // Considérer tous les produits comme disponibles par défaut
    final bool isInStock = true;

    // Traiter l'URL de l'image
    String imageUrl = '';
    if (element['photo_kit'] != null &&
        element['photo_kit'].toString().isNotEmpty) {
      // Utiliser la méthode ImageUtils pour formater l'URL
      imageUrl = ImageUtils.formatImageUrl(element['photo_kit'].toString());

      // Afficher l'URL pour débogage
      print("URL d'image formatée: $imageUrl");
    }

    // Afficher la boîte de dialogue avec les détails
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.9,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.white, Colors.grey.shade50],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 25,
                  offset: const Offset(0, -10),
                ),
              ],
            ),
            child: Column(
              children: [
                // Header moderne avec handle bar et bouton fermer
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  child: Column(
                    children: [
                      // Handle bar moderne
                      Container(
                        width: 50,
                        height: 5,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Header avec titre et bouton fermer
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'Détails du produit',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.color.textColor,
                              ),
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: () => Navigator.pop(context),
                              icon: Icon(
                                Icons.close_rounded,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                Expanded(
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Column(
                      children: [
                        // Image hero avec design moderne
                        Container(
                          height: 280,
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(25),
                            child: Stack(
                              fit: StackFit.expand,
                              children: [
                                imageUrl.isNotEmpty
                                    ? Image.network(
                                      imageUrl,
                                      fit: BoxFit.cover,
                                      errorBuilder: (
                                        context,
                                        error,
                                        stackTrace,
                                      ) {
                                        return Container(
                                          decoration: BoxDecoration(
                                            gradient: LinearGradient(
                                              begin: Alignment.topLeft,
                                              end: Alignment.bottomRight,
                                              colors: [
                                                AppTheme.color.primaryColor
                                                    .withValues(alpha: 0.3),
                                                AppTheme.color.primaryColor
                                                    .withValues(alpha: 0.1),
                                              ],
                                            ),
                                          ),
                                          child: Center(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons
                                                      .image_not_supported_rounded,
                                                  size: 80,
                                                  color: Colors.white,
                                                ),
                                                const SizedBox(height: 12),
                                                Text(
                                                  'Image non disponible',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      },
                                    )
                                    : Container(
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            AppTheme.color.primaryColor
                                                .withValues(alpha: 0.3),
                                            AppTheme.color.primaryColor
                                                .withValues(alpha: 0.1),
                                          ],
                                        ),
                                      ),
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.inventory_2_rounded,
                                              size: 80,
                                              color: Colors.white,
                                            ),
                                            const SizedBox(height: 12),
                                            Text(
                                              'Produit',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                // Overlay gradient pour améliorer la lisibilité
                                Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Colors.transparent,
                                        Colors.black.withValues(alpha: 0.1),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Informations principales avec design moderne
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Badge de disponibilité moderne
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isInStock
                                              ? AppTheme.color.greenColor
                                                  .withValues(alpha: 0.1)
                                              : AppTheme.color.redColor
                                                  .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          isInStock
                                              ? Icons.check_circle_rounded
                                              : Icons.error_rounded,
                                          size: 16,
                                          color:
                                              isInStock
                                                  ? AppTheme.color.greenColor
                                                  : AppTheme.color.redColor,
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          isInStock
                                              ? 'Disponible'
                                              : 'Indisponible',
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color:
                                                isInStock
                                                    ? AppTheme.color.greenColor
                                                    : AppTheme.color.redColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 20),

                              // Nom du produit avec style moderne
                              Text(
                                name,
                                style: TextStyle(
                                  fontSize: 26,
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.color.textColor,
                                  letterSpacing: -0.5,
                                  height: 1.2,
                                ),
                              ),

                              const SizedBox(height: 20),

                              // Section prix avec design moderne
                              Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      AppTheme.color.primaryColor.withValues(
                                        alpha: 0.1,
                                      ),
                                      AppTheme.color.primaryColor.withValues(
                                        alpha: 0.05,
                                      ),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(10),
                                          decoration: BoxDecoration(
                                            color: AppTheme.color.primaryColor
                                                .withValues(alpha: 0.2),
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                          ),
                                          child: Icon(
                                            Icons.monetization_on_rounded,
                                            color: AppTheme.color.primaryColor,
                                            size: 24,
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Prix total',
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: Colors.grey.shade600,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                '$price FCFA',
                                                style: TextStyle(
                                                  fontSize: 22,
                                                  fontWeight: FontWeight.bold,
                                                  color:
                                                      AppTheme
                                                          .color
                                                          .primaryColor,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    Container(
                                      padding: const EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withValues(
                                          alpha: 0.8,
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Durée',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.grey.shade600,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  '$duration jours',
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color:
                                                        AppTheme
                                                            .color
                                                            .orangeColor,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Container(
                                            width: 1,
                                            height: 40,
                                            color: Colors.grey.shade300,
                                          ),
                                          const SizedBox(width: 16),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'Journalier',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.grey.shade600,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  '$journalier FCFA',
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color:
                                                        AppTheme
                                                            .color
                                                            .greenColor,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Section avertissement importante
                              Container(
                                padding: const EdgeInsets.all(20),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                    width: 1,
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.description_rounded,
                                          color: AppTheme.color.primaryColor,
                                          size: 24,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Article important',
                                          style: TextStyle(
                                            fontSize: 19,
                                            fontWeight: FontWeight.bold,
                                            color: AppTheme.color.redColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Text(
                                      description,
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: AppTheme.color.textColor,
                                        height: 1.5,
                                      ),
                                    ),
                                    Text(
                                      description2,
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: AppTheme.color.redColor,
                                        height: 1.5,
                                      ),
                                    ),
                                    RichText(
                                      maxLines: 20,
                                      text: TextSpan(
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: AppTheme.color.textColor,
                                          height: 1.5,
                                        ),
                                        children: [
                                          TextSpan(text: description3 + ' '),
                                          TextSpan(
                                            text: description4 + ' ',
                                            style: TextStyle(
                                              color: AppTheme.color.redColor,
                                            ),
                                          ),
                                          TextSpan(text: description5),
                                        ],
                                      ),
                                    ),
                                    Text(
                                      description6,
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: AppTheme.color.redColor,
                                        height: 1.5,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Bouton d'action moderne en bas
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(30),
                      bottomRight: Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 15,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: SafeArea(
                    child: Container(
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.color.primaryColor,
                            AppTheme.color.primaryColor.withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.color.primaryColor.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _showPaymentMethodDialog(element);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.shopping_cart_rounded, size: 22),
                            const SizedBox(width: 12),
                            Text(
                              'Commander maintenant',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  // Méthode pour formater les prix
  String _formatPrice(dynamic price) {
    if (price == null) return 'N/A';

    // Convertir en nombre si c'est une chaîne
    int numericPrice;
    if (price is String) {
      numericPrice = int.tryParse(price) ?? 0;
    } else if (price is int) {
      numericPrice = price;
    } else if (price is double) {
      numericPrice = price.toInt();
    } else {
      return 'N/A';
    }

    // Formater avec séparateurs de milliers
    return numericPrice.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]} ',
    );
  }

  // Ancienne méthode supprimée - remplacée par _showPaymentMethodDialog

  /// Dialogue de sélection de méthode de paiement pour versement échelonné
  void _showInstallmentPaymentMethodDialog(
    Map<String, dynamic> item,
    int versementAmount,
    int totalPrice,
  ) {
    final String name = item['option_kit'] ?? item['nom'] ?? 'Pack sans nom';
    final int restantAPayer = totalPrice - versementAmount;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Paiement Journalier - Choisissez votre méthode',
              style: TextStyle(
                color: AppTheme.color.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Résumé du versement
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.color.orangeColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.color.orangeColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Versement:',
                            style: TextStyle(
                              color: AppTheme.color.orangeColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            '${_formatPrice(versementAmount)} FCFA',
                            style: TextStyle(
                              color: AppTheme.color.orangeColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Restant à payer:',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '${_formatPrice(restantAPayer)} FCFA',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // Options de paiement
                _buildPaymentMethodOption(
                  icon: Icons.phone_android,
                  title: 'Paiement Mobile',
                  subtitle: 'Orange Money, MTN Money, Moov Money',
                  color: const Color(0xFF4CAF50),
                  onTap: () {
                    Navigator.of(context).pop();
                    _processInstallmentOrderWithCinetPay(item, versementAmount);
                  },
                ),

                const SizedBox(height: 12),

                _buildPaymentMethodOption(
                  icon: Icons.waves,
                  title: 'WAVE CI',
                  subtitle: 'Paiement instantané via WAVE CI',
                  color: const Color.fromARGB(255, 54, 115, 255),
                  onTap: () {
                    Navigator.of(context).pop();
                    _processInstallmentOrderWithWave(item, versementAmount);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  'Annuler',
                  style: TextStyle(color: AppTheme.color.brunGris),
                ),
              ),
            ],
          ),
    );
  }

  /// Widget pour afficher une option de méthode de paiement
  Widget _buildPaymentMethodOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
          color: Colors.grey.shade50,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _processInstallmentOrderWithCinetPay(
    Map<String, dynamic> item,
    int versementAmount,
  ) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );
      final user = await AuthService.getUserData();

      final String itemId =
          item['id_kit']?.toString() ?? item['id']?.toString() ?? '';

      if (itemId.isEmpty) {
        if (mounted) Navigator.pop(context);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Impossible d\'identifier le produit. Veuillez réessayer.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // ÉTAPE 1: Effectuer le paiement d'abord avec CinetPay
      final paymentResult = await CinetPayService.initializePayment(
        context: context,
        amount: versementAmount.toDouble(),
        description:
            'Versement pour ${item['option_kit'] ?? item['nom'] ?? 'Produit'}',
        customerName: user?['nom_client'] ?? 'Client Callitris',
        customerEmail: user?['email'] ?? '<EMAIL>',
        customerPhone: user?['telephone_client'] ?? '0101010101',
        metadata: {
          'type': 'direct_payment',
          'productId': itemId,
          'productName': item['option_kit'] ?? item['nom'] ?? 'Produit',
          'isCarnet': true,
        },
      );

      if (paymentResult['success']) {
        final transaction = paymentResult['transaction'];

        // Vérifier le contexte avant l'appel async
        if (!mounted) return;

        // Lancer le processus de paiement
        final processResult = await CinetPayService.processPayment(
          context: context,
          amount: versementAmount.toDouble(),
          customerPhone: user?['telephone_client'] ?? '0101010101',
          transaction: transaction,
        );

        if (processResult['success']) {
          // ÉTAPE 2: Vérifier le statut de la transaction via l'API CinetPay
          // Pour l'instant, on considère que processResult['success'] indique ACCEPTED/SUCCEEDED
          final transactionStatus = 'ACCEPTED'; // Simulé pour l'instant

          if (transactionStatus == 'ACCEPTED' ||
              transactionStatus == 'SUCCEEDED') {
            // ÉTAPE 3: Si statut ACCEPTED/SUCCEEDED → Créer la commande
            final orderResult = await OrderService.placeOrder(
              itemId,
              isCarnet: true,
            );

            if (orderResult['success']) {
              // ÉTAPE 4: Récupérer les informations de la commande
              final Map<String, dynamic>? orderData =
                  (orderResult['orderData'] as Map?)?.cast<String, dynamic>();
              String? orderId =
                  orderData?['id']?.toString() ??
                  orderData?['orderId']?.toString() ??
                  orderData?['commande_id']?.toString();
              final String? commandeKey =
                  orderData?['cle']?.toString() ??
                  orderData?['commandeKey']?.toString() ??
                  orderData?['cle_commande']?.toString();

              if (orderId != null) {
                // ÉTAPE 5: Enregistrer le premier versement (montant de la transaction)
                final versementResult = await OrderService.addVersement(
                  orderId,
                  versementAmount.toDouble(),
                  commandeKey: commandeKey,
                );

                // Fermer l'indicateur de chargement
                if (mounted) Navigator.pop(context);

                if (versementResult['success'] && mounted) {
                  // Afficher le dialogue de succès
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder:
                        (_) => AlertDialog(
                          title: const Text('Paiement réussi'),
                          content: Text(
                            'Votre versement de ${_formatPrice(versementAmount)} FCFA a été enregistré.',
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('Continuer'),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => const MyOrdersScreen(),
                                  ),
                                );
                              },
                              child: const Text('Voir mes commandes'),
                            ),
                          ],
                        ),
                  );
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          versementResult['message'] ??
                              'Erreur lors de l\'enregistrement du versement',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              } else {
                // Fermer l'indicateur de chargement
                if (mounted) Navigator.pop(context);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Erreur: ID de commande introuvable'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            } else {
              // Fermer l'indicateur de chargement
              if (mounted) Navigator.pop(context);

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      orderResult['message'] ??
                          'Erreur lors de la création de la commande',
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          } else {
            // Fermer l'indicateur de chargement
            if (mounted) Navigator.pop(context);

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Transaction non acceptée. Statut: $transactionStatus',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else {
          // Fermer l'indicateur de chargement
          if (mounted) Navigator.pop(context);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(processResult['message'] ?? 'Erreur de paiement'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // Fermer l'indicateur de chargement
        if (mounted) Navigator.pop(context);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                paymentResult['message'] ?? 'Erreur d\'initialisation',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Fermer l'indicateur de chargement en cas d'erreur
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// Traite le versement échelonné avec Wave
  Future<void> _processInstallmentOrderWithWave(
    Map<String, dynamic> item,
    int versementAmount,
  ) async {
    try {
      // Afficher un indicateur de chargement
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Récupérer l'ID de l'élément
      final String itemId =
          item['id_kit']?.toString() ?? item['id']?.toString() ?? '';

      if (itemId.isEmpty) {
        // Fermer le dialogue de chargement
        if (mounted) Navigator.pop(context);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Impossible d\'identifier le produit. Veuillez réessayer.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // ÉTAPE 2: Effectuer le paiement Wave et sauvegarder les infos de transaction
      // Vérifier le contexte avant l'appel async
      if (!mounted) return;

      // ÉTAPE 1: Effectuer le paiement Wave (commandeId peut être vide, backend gère)
      final paymentResult = await OrderService.addVersementWithWave(
        context: context,
        commandeId: '', // Backend gère le cas null/vide
        montant: versementAmount.toDouble(),
        commandeKey: null, // Peut être null
        monnaie: 0,
        itemId: itemId,
      );

      // ÉTAPE 3: Vérifier le statut de la transaction Wave
      if (paymentResult['success']) {
        // Interroger l'API Wave pour vérifier le statut final
        if (paymentResult['paymentId'] != null) {
          final SharedPreferences prefs = await SharedPreferences.getInstance();
          await prefs.setString('payment_code', paymentResult['paymentId']);
        }

        _buildPaymentStatusWidget();
      } else {
        // Fermer l'indicateur de chargement
        if (mounted) Navigator.pop(context);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                paymentResult['message'] ?? 'Erreur de paiement Wave',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Fermer l'indicateur de chargement en cas d'erreur
      if (mounted) Navigator.pop(context);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _buildPaymentStatusWidget() async {
    // Méthode simplifiée pour vérifier le statut de paiement
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? paymentCode = prefs.getString('payment_code');

      if (paymentCode != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Vérification du statut de paiement...'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      print('Erreur lors de la vérification du statut: $e');
    }
  }

  void _showPaymentSuccess(String status) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: _buildStatusCard(status),
        );
      },
    );
  }

  Widget _buildStatusCard(String status) {
    String title;
    String message;
    Color color;
    IconData icon;
    List<Widget> actions;

    switch (status) {
      case 'succeeded':
      case 'ACCEPTED':
        title = 'Paiement réussi !';
        message =
            'Votre paiement a été traité avec succès. Votre commande va être enregistrée.';
        color = Colors.green;
        icon = Icons.check_circle;
        actions = [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _processSuccessfulPayment();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: const Text('Continuer'),
          ),
        ];
        break;
      case 'processing':
      case 'PENDING':
      case 'failed':
      default:
        title = 'Paiement échoué';
        message = 'Votre paiement a échoué. Veuillez réessayer.';
        color = Colors.red;
        icon = Icons.error;
        actions = [
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _clearPendingTransaction();
              await _clearPaymentCodeAndReload();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: const Text('Annuler'),
          ),
        ];
        break;
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 64, color: color),
        const SizedBox(height: 16),
        Text(
          title,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 12),
        Text(
          message,
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: actions,
        ),
      ],
    );
  }

  void _showOrderSuccess(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Commande confirmée',
              style: TextStyle(
                color: AppTheme.color.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppTheme.color.greenColor,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(message, textAlign: TextAlign.center),
                const SizedBox(height: 8),
                const Text(
                  'Vous pouvez suivre l\'état de votre commande dans la section "Mes commandes".',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Continuer'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MyOrdersScreen(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.color.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Voir mes commandes'),
              ),
            ],
          ),
    );
  }

  void _showOrderError(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text(
              'Erreur',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.error, color: Colors.red, size: 64),
                const SizedBox(height: 16),
                Text(message, textAlign: TextAlign.center),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}
